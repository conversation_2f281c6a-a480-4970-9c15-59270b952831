import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:storetrack_app/config/themes/app_colors.dart';
import 'package:storetrack_app/core/utils/snackbar_service.dart';
import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart'
    as entities;
import 'package:storetrack_app/shared/widgets/custom_app_bar.dart';
import 'package:storetrack_app/features/home/<USER>/widgets/question_card.dart';
import 'package:storetrack_app/features/home/<USER>/widgets/comment_card.dart';
import 'package:storetrack_app/features/home/<USER>/widgets/empty_questions_state.dart';
import 'package:storetrack_app/core/database/realm_database.dart';
import 'package:storetrack_app/features/home/<USER>/models/task_detail_model.dart';
import 'package:storetrack_app/config/routes/app_router.gr.dart';

@RoutePage()
class QuestionPage extends StatefulWidget {
  final entities.Form form;
  final num? taskId;

  const QuestionPage({
    super.key,
    required this.form,
    this.taskId,
  });

  @override
  State<QuestionPage> createState() => _QuestionPageState();
}

class _QuestionPageState extends State<QuestionPage> {
  // State for comment fields
  final Map<num, String?> _selectedCommentTypes = {};
  final Map<num, TextEditingController> _commentControllers = {};

  // State for question visibility based on conditional logic
  final Map<num, bool> _questionVisibility = {};

  // State for comment validation
  final Map<num, String?> _commentValidationErrors = {};

  // Photo refresh counter to trigger photo reload in QuestionCard widgets
  int _photoRefreshCounter = 0;

  // Get questions from the form
  List<entities.Question>? get questionItems => widget.form.questions;

  // Get visible questions based on conditional logic
  List<entities.Question> get visibleQuestions {
    if (questionItems == null) return [];

    final visible = questionItems!.where((question) {
      // Always show questions without questionId (like comment items)
      if (question.questionId == null) return true;
      // Show questions based on visibility state (default to true if not set)
      final isVisible = _questionVisibility[question.questionId!] ?? true;
      return isVisible;
    }).toList();

    debugPrint(
        'Total questions: ${questionItems!.length}, Visible questions: ${visible.length}');
    return visible;
  }

  @override
  void initState() {
    super.initState();
    if (widget.form.questions != null) {
      for (final question in widget.form.questions!) {
        if (question.isComment == true && question.questionId != null) {
          final questionId = question.questionId!;
          _commentControllers[questionId] = TextEditingController();
          // Initialize with null or a default first item if applicable
          _selectedCommentTypes[questionId] = null;

          // Add listener to clear validation errors when user types
          _commentControllers[questionId]!.addListener(() {
            if (_commentValidationErrors.containsKey(questionId)) {
              setState(() {
                _commentValidationErrors[questionId] = null;
              });
            }
          });
        }
      }
    }

    // Initialize question visibility and process conditional logic
    _initializeQuestionVisibility();
    _processQuestionConditionalLogic();

    // Load saved comment data
    _loadSavedCommentData();
  }

  @override
  void dispose() {
    _commentControllers.forEach((_, controller) => controller.dispose());
    super.dispose();
  }

  /// Initialize question visibility based on conditional logic
  /// All questions should be visible by default
  void _initializeQuestionVisibility() {
    if (widget.form.questions == null) return;

    debugPrint(
        'Initializing visibility for ${widget.form.questions!.length} questions');

    for (final question in widget.form.questions!) {
      if (question.questionId == null) continue;

      // All questions are visible by default
      _questionVisibility[question.questionId!] = true;
    }

    debugPrint(
        'Initialized visibility for ${_questionVisibility.length} questions');

    // Apply restricted multi-parent question visibility logic
    _handleRestrictedMultiQuestionVisibility();
  }

  /// Process question conditional logic based on saved QuestionAnswer data from QPMDPage
  void _processQuestionConditionalLogic() {
    if (widget.taskId == null || widget.form.questions == null) return;

    try {
      final realm = RealmDatabase.instance.realm;

      // Find the task with the matching taskId
      final taskModel = realm.query<TaskDetailModel>(
          'taskId == \$0', [widget.taskId!.toInt()]).firstOrNull;

      if (taskModel == null) {
        // Task not found in database
        return;
      }

      // Find the form with the matching formId
      final formModel = taskModel.forms
          .where((form) => form.formId == widget.form.formId!.toInt())
          .firstOrNull;

      if (formModel == null) {
        // Form not found in task
        return;
      }

      // Get all saved QuestionAnswer objects for this form
      final savedAnswers = formModel.questionAnswers;

      // Process each question's conditions
      for (final question in widget.form.questions!) {
        if (question.questionConditions == null ||
            question.questionConditions!.isEmpty) {
          continue;
        }

        // Process each condition for this question
        for (final condition in question.questionConditions!) {
          if (condition.measurementId == null ||
              condition.measurementOptionId == null ||
              condition.actionQuestionId == null ||
              condition.action == null) {
            continue;
          }

          // Find matching saved answer for this measurement
          final matchingAnswer = savedAnswers
              .where((answer) =>
                  answer.measurementId == condition.measurementId!.toInt() &&
                  answer.measurementOptionId ==
                      condition.measurementOptionId!.toInt())
              .firstOrNull;

          if (matchingAnswer != null) {
            // Apply the conditional action
            _applyQuestionConditionalAction(
              condition.actionQuestionId!,
              condition.action!,
            );
          }
        }
      }

      // Apply restricted multi-parent question visibility logic after processing conditions
      _handleRestrictedMultiQuestionVisibility();

      // Update UI after processing all conditional logic
      setState(() {});
    } catch (e) {
      // Error processing question conditional logic - silently continue
    }
  }

  /// Apply conditional action (show/hide) to a target question
  void _applyQuestionConditionalAction(num targetQuestionId, String action) {
    final previousVisibility = _questionVisibility[targetQuestionId] ?? true;

    if (action.toLowerCase() == 'appear') {
      _questionVisibility[targetQuestionId] = true;
    } else if (action.toLowerCase() == 'disappear') {
      _questionVisibility[targetQuestionId] = false;
    }

    // Debug: Log visibility changes
    final newVisibility = _questionVisibility[targetQuestionId] ?? true;
    if (previousVisibility != newVisibility) {
      debugPrint(
          'Question $targetQuestionId visibility changed: $previousVisibility -> $newVisibility (action: $action)');
    }
  }

  /// Handle restricted multi question visibility logic
  /// If any question meets the restrictedMultiQuestion criteria:
  /// - Initially: Hide the restrictedMultiQuestion, show all other questions
  /// - After filling: Show ALL questions when any other question has saved counter value > 0
  void _handleRestrictedMultiQuestionVisibility() {
    if (widget.form.questions == null || widget.form.questions!.isEmpty) return;

    try {
      // Step 1: Identify restrictedMultiQuestion
      entities.Question? restrictedMultiQuestion;

      for (final question in widget.form.questions!) {
        if (question.isMulti == true &&
            question.multiMeasurementId != null &&
            question.multiMeasurementId != 0) {
          restrictedMultiQuestion = question;
          debugPrint(
              'Found restrictedMultiQuestion: ${question.questionId} - ${question.questionDescription}');
          break;
        }
      }

      // Step 2: If no restrictedMultiQuestion found, return early
      if (restrictedMultiQuestion == null) {
        debugPrint('No restrictedMultiQuestion found');
        return;
      }

      // Step 3: Check if any OTHER question (not the restrictedMultiQuestion) has saved counter value > 0
      final hasOtherQuestionWithValue =
          _hasOtherQuestionWithCounterValue(restrictedMultiQuestion);
      debugPrint(
          'Other questions have counter value > 0: $hasOtherQuestionWithValue');

      // Step 4: Apply visibility rules
      if (hasOtherQuestionWithValue) {
        // Make ALL questions visible (including restrictedMultiQuestion) when other questions have value > 0
        for (final question in widget.form.questions!) {
          if (question.questionId != null) {
            _questionVisibility[question.questionId!] = true;
          }
        }
        debugPrint(
            'Made all questions visible due to other questions having counter value > 0');
      } else {
        // Initially: Hide the restrictedMultiQuestion, show all other questions
        for (final question in widget.form.questions!) {
          if (question.questionId != null) {
            if (question.questionId == restrictedMultiQuestion.questionId) {
              // Hide the restrictedMultiQuestion
              _questionVisibility[question.questionId!] = false;
            } else {
              // Show all other questions
              _questionVisibility[question.questionId!] = true;
            }
          }
        }
        debugPrint(
            'Hidden restrictedMultiQuestion, showing all other questions');
      }
    } catch (e) {
      debugPrint('Error in _handleRestrictedMultiQuestionVisibility: $e');
    }
  }

  /// Check if any OTHER question (not the restrictedMultiQuestion) has saved counter value > 0
  /// Returns true if any other question has a counter value > 0 in the QuestionAnswer database
  bool _hasOtherQuestionWithCounterValue(
      entities.Question restrictedMultiQuestion) {
    if (widget.taskId == null || widget.form.questions == null) {
      return false;
    }

    try {
      final realm = RealmDatabase.instance.realm;

      // Find the task with the matching taskId
      final taskModel = realm.query<TaskDetailModel>(
          'taskId == \$0', [widget.taskId!.toInt()]).firstOrNull;

      if (taskModel == null) {
        debugPrint('Task not found in database for taskId: ${widget.taskId}');
        return false;
      }

      // Find the form with the matching formId
      final formModel = taskModel.forms
          .where((form) => form.formId == widget.form.formId!.toInt())
          .firstOrNull;

      if (formModel == null) {
        debugPrint('Form not found in task for formId: ${widget.form.formId}');
        return false;
      }

      // Check all questions except the restrictedMultiQuestion
      for (final question in widget.form.questions!) {
        if (question.questionId == null ||
            question.questionId == restrictedMultiQuestion.questionId) {
          continue; // Skip the restrictedMultiQuestion itself
        }

        // Look for QuestionAnswer entries for this question with counter values
        final questionAnswers = formModel.questionAnswers
            .where(
                (answer) => answer.questionId == question.questionId!.toInt())
            .toList();

        for (final answer in questionAnswers) {
          if (answer.measurementTextResult != null) {
            final counterValue = int.tryParse(answer.measurementTextResult!);
            if (counterValue != null && counterValue > 0) {
              debugPrint(
                  'Found counter value > 0 in other question: ${question.questionId}, value: $counterValue');
              return true;
            }
          }
        }
      }

      debugPrint('No other questions have counter value > 0');
      return false;
    } catch (e) {
      debugPrint('Error checking other questions counter values: $e');
      return false;
    }
  }

  /// Load saved comment data from database
  void _loadSavedCommentData() {
    if (widget.taskId == null || widget.form.formId == null) return;

    try {
      final realm = RealmDatabase.instance.realm;

      // Find the task with the matching taskId
      final taskModel = realm.query<TaskDetailModel>(
          'taskId == \$0', [widget.taskId!.toInt()]).firstOrNull;

      if (taskModel == null) return;

      // Find the form with the matching formId
      final formModel = taskModel.forms
          .where((form) => form.formId == widget.form.formId!.toInt())
          .firstOrNull;

      if (formModel == null) return;

      // Get saved comment answers
      final savedCommentAnswers = formModel.questionAnswers
          .where((qa) => qa.isComment == true)
          .toList();

      // Restore comment data
      for (final commentAnswer in savedCommentAnswers) {
        if (commentAnswer.questionId != null) {
          final questionId = commentAnswer.questionId!;

          // Restore text field value
          if (_commentControllers.containsKey(questionId) &&
              commentAnswer.measurementTextResult != null) {
            _commentControllers[questionId]!.text =
                commentAnswer.measurementTextResult!;
          }

          // Restore dropdown selection
          if (commentAnswer.commentTypeId != null) {
            // Find the comment type by ID
            final question = widget.form.questions?.firstWhere(
                (q) => q.questionId == questionId,
                orElse: () => entities.Question());

            if (question?.commentTypes != null) {
              final commentType = question!.commentTypes!.firstWhere(
                  (ct) => ct.commentTypeId == commentAnswer.commentTypeId,
                  orElse: () => entities.CommentType());

              if (commentType.commentType != null) {
                setState(() {
                  _selectedCommentTypes[questionId] = commentType.commentType;
                });
              }
            }
          }
        }
      }
    } catch (e) {
      debugPrint('Error loading saved comment data: $e');
    }
  }

  /// Validate comment fields
  bool _validateCommentFields() {
    // Pre-condition check: Only proceed if form.min_qty == 0
    if (widget.form.minQty != 0) {
      return true; // Skip validation if min_qty is not 0
    }

    bool isValid = true;
    setState(() {
      _commentValidationErrors.clear();
    });

    if (widget.form.questions == null) return isValid;

    for (final question in widget.form.questions!) {
      if (question.isComment == true &&
          question.questionId != null &&
          question.isCommentMandatory == true) {
        final questionId = question.questionId!;
        final controller = _commentControllers[questionId];
        final selectedType = _selectedCommentTypes[questionId];

        // Check if comment has dropdown options
        final hasDropdownOptions =
            question.commentTypes != null && question.commentTypes!.isNotEmpty;

        // Validate based on comment type
        if (hasDropdownOptions) {
          // For dropdown comments, check if dropdown is selected
          if (selectedType == null || selectedType.isEmpty) {
            setState(() {
              _commentValidationErrors[questionId] = 'This field is required';
            });
            isValid = false;
          }
        }

        // Always validate text field for mandatory comments
        if (controller == null || controller.text.trim().isEmpty) {
          setState(() {
            _commentValidationErrors[questionId] = 'This field is required';
          });
          isValid = false;
        }
      }
    }

    return isValid;
  }

  /// Generate QuestionAnswer objects for comments
  List<entities.QuestionAnswer> _generateCommentAnswers() {
    final commentAnswers = <entities.QuestionAnswer>[];

    if (widget.form.questions == null) return commentAnswers;

    for (final question in widget.form.questions!) {
      if (question.isComment == true && question.questionId != null) {
        final questionId = question.questionId!;
        final controller = _commentControllers[questionId];
        final selectedType = _selectedCommentTypes[questionId];

        // Only save if there's actual content
        if ((controller != null && controller.text.trim().isNotEmpty) ||
            (selectedType != null && selectedType.isNotEmpty)) {
          // Find comment type ID if dropdown is selected
          num? commentTypeId;
          if (selectedType != null && question.commentTypes != null) {
            final commentType = question.commentTypes!.firstWhere(
                (ct) => ct.commentType == selectedType,
                orElse: () => entities.CommentType());
            commentTypeId = commentType.commentTypeId;
          }

          final questionAnswer = entities.QuestionAnswer(
            taskId: widget.taskId,
            formId: widget.form.formId,
            questionId: questionId,
            questionpartId: null,
            measurementId: null,
            measurementTypeId: null,
            measurementOptionId: null,
            measurementTextResult: controller?.text.trim(),
            isComment: true,
            commentTypeId: commentTypeId,
          );

          commentAnswers.add(questionAnswer);
        }
      }
    }

    return commentAnswers;
  }

  /// Save comment answers to database
  Future<bool> _saveCommentAnswersToDatabase(
      List<entities.QuestionAnswer> commentAnswers) async {
    if (widget.taskId == null || widget.form.formId == null) {
      debugPrint('TaskId or FormId is null, cannot save comment answers');
      return false;
    }

    try {
      final realm = RealmDatabase.instance.realm;

      // Find the task with the matching taskId
      final taskModel = realm.query<TaskDetailModel>(
          'taskId == \$0', [widget.taskId!.toInt()]).firstOrNull;

      if (taskModel == null) {
        debugPrint('Task not found in database');
        return false;
      }

      // Find the form with the matching formId
      final formModel = taskModel.forms
          .where((form) => form.formId == widget.form.formId!.toInt())
          .firstOrNull;

      if (formModel == null) {
        debugPrint('Form not found in task');
        return false;
      }

      // Convert QuestionAnswer entities to models
      final commentAnswerModels = commentAnswers
          .map((qa) => QuestionAnswerModel(
                taskId: qa.taskId?.toInt(),
                formId: qa.formId?.toInt(),
                questionId: qa.questionId?.toInt(),
                questionpartId: qa.questionpartId?.toInt(),
                measurementId: qa.measurementId?.toInt(),
                measurementTypeId: qa.measurementTypeId?.toInt(),
                measurementOptionId: qa.measurementOptionId?.toInt(),
                measurementTextResult: qa.measurementTextResult,
                isComment: qa.isComment,
                commentTypeId: qa.commentTypeId?.toInt(),
              ))
          .toList();

      // Save to database in a write transaction
      realm.write(() {
        // Remove existing comment answers for this form
        final existingCommentAnswers = formModel.questionAnswers
            .where((qa) => qa.isComment == true)
            .toList();

        for (final existingAnswer in existingCommentAnswers) {
          formModel.questionAnswers.remove(existingAnswer);
        }

        // Add new comment answers
        for (final newAnswer in commentAnswerModels) {
          formModel.questionAnswers.add(newAnswer);
        }
      });

      debugPrint(
          'Successfully saved ${commentAnswerModels.length} comment answers to database');
      return true;
    } catch (e) {
      debugPrint('Error saving comment answers to database: $e');
      return false;
    }
  }

  /// Handle save button press
  void _handleSave() async {
    if (_validateCommentFields()) {
      final commentAnswers = _generateCommentAnswers();
      debugPrint(
          'Generated ${commentAnswers.length} comment answers for saving');

      // Save comment answers to database
      final saveSuccess = await _saveCommentAnswersToDatabase(commentAnswers);

      if (mounted) {
        if (saveSuccess) {
          SnackBarService.success(
            context: context,
            message: 'Comments saved successfully!',
          );
          // Navigate back
          Navigator.of(context).pop();
        } else {
          SnackBarService.error(
            context: context,
            message: 'Failed to save comments. Please try again.',
          );
        }
      }
    } else {
      SnackBarService.error(
        context: context,
        message: 'Please fill in all required comment fields.',
      );
    }
  }

  /// Check if a question is mandatory based on the complex logic provided
  bool _isQuestionMandatory(entities.Question question) {
    if (question.isMulti != true && question.isComment != true) {
      // Loop through measurements, then loop through measurement_validations to check if any "required" key is true
      if (question.measurements != null) {
        for (final measurement in question.measurements!) {
          if (measurement.measurementValidations != null) {
            for (final validation in measurement.measurementValidations!) {
              if (validation.required == true) {
                return true;
              }
            }
          }
        }
      }
    } else if (question.isMulti != true) {
      // Check if isSupplementaryQuestion is true and isOneOption is true
      final isSupplementaryQuestion = question.multiMeasurementId != null &&
          question.multiMeasurementId.toString() != "0" &&
          question.multiMeasurementId.toString().isNotEmpty;

      if (isSupplementaryQuestion) {
        // Check if isOneOption is true (any questionpart_id contains "-")
        if (question.questionParts != null) {
          for (final questionPart in question.questionParts!) {
            if (questionPart.questionpartId != null &&
                questionPart.questionpartId.toString().contains("-")) {
              return true;
            }
          }
        }
      }
    }
    return false;
  }

  /// Get camera icon info for a question based on photo_tags_two array
  Map<String, dynamic> _getCameraIconInfo(entities.Question question) {
    final result = {'show': false, 'isMandatory': false};

    if (question.photoTagsTwo == null || question.photoTagsTwo!.isEmpty) {
      return result;
    }

    result['show'] = true;

    // Check if any item has is_mandatory set to true
    for (final photoTag in question.photoTagsTwo!) {
      if (photoTag.isMandatory == true) {
        result['isMandatory'] = true;
        break;
      }
    }

    return result;
  }

  /// Check if question has a photo URL
  bool _hasPhotoUrl(entities.Question question) {
    return question.photoUrl != null && question.photoUrl!.isNotEmpty;
  }

  /// Handle question tap navigation - moved from QuestionCard
  void _handleQuestionTap(entities.Question question) async {
    final questionParts = question.questionParts ?? [];
    final hasSignature = question.hasSignature ?? false;
    final isMulti = question.isMulti ?? false;

    if (questionParts.length != 1 || hasSignature) {
      if (isMulti) {
        // Navigate to FQPDPage for multi questions
        await context.pushRoute(FQPDRoute(
          question: question,
          taskId: widget.taskId,
          formId: widget.form.formId,
        ));
      } else {
        // Navigate to SubHeaderPage for questions with multiple parts or signature
        await context.pushRoute(SubHeaderRoute(
          title: question.questionDescription ?? 'Details',
          questionParts: questionParts,
          question: question,
          taskId: widget.taskId,
          formId: widget.form.formId,
        ));
      }
    } else {
      // Navigate to QPMDPage for single measurement questions
      await context.pushRoute(QPMDRoute(
        question: question,
        questionPart: questionParts.first,
        taskId: widget.taskId,
        formId: widget.form.formId,
      ));
    }

    // Refresh page state after returning from navigation
    _refreshPageState();
  }

  /// Handle photo section tap navigation - moved from QuestionCard
  void _handleAddPhotosTap(entities.Question question) async {
    final showCameraIcon = question.photoTagsTwo?.isNotEmpty == true ||
        question.photoTagsThree?.isNotEmpty == true;

    if (showCameraIcon &&
        (question.photoTagsTwo?.isNotEmpty == true ||
            question.photoTagsThree?.isNotEmpty == true)) {
      // Navigate to MPTPage with question and level
      final level = question.photoTagsTwo?.isNotEmpty == true ? 2 : 3;
      await context.pushRoute(MPTRoute(
        taskId: widget.taskId?.toString(),
        formId: widget.form.formId?.toString(),
        questionId: question.questionId?.toString(),
        images: const [],
        question: question,
        level: level,
        questionPartId: null,
      ));

      // Refresh page state after returning from navigation
      _refreshPageState();
    }
  }

  /// Refresh page state after returning from navigation
  /// This updates progress indicators and question visibility based on latest data
  void _refreshPageState() {
    if (mounted) {
      // Reprocess conditional logic to update question visibility
      _processQuestionConditionalLogic();

      // Reload comment data in case it was updated
      _loadSavedCommentData();

      // Increment photo refresh counter to trigger photo reload in QuestionCard widgets
      _photoRefreshCounter++;

      // Trigger UI rebuild to reflect updated progress and visibility
      setState(() {});
    }
  }

  /// Calculate progress for a question based on saved QuestionAnswer data
  Map<String, dynamic> _calculateQuestionProgress(entities.Question question) {
    double progress = 0.0;
    String progressText = '0 of 0';

    if (question.measurements == null || question.measurements!.isEmpty) {
      return {'progress': progress, 'progressText': progressText};
    }

    // Check if this is a multi-question that navigates to FQPDPage
    final isMultiQuestion = question.isMulti == true;

    if (isMultiQuestion) {
      // Handle multi-questions with dynamic form count display logic
      return _calculateMultiQuestionProgress(question);
    }

    // Calculate total expected answers based on measurements and question parts
    // For questions with multiple parts (that navigate through SubHeaderPage),
    // each measurement needs to be answered for each question part
    final questionPartCount = question.questionParts?.length ?? 1;

    // Check if this question navigates to QPMDPage (single part questions)
    final questionParts = question.questionParts ?? [];
    final hasSignature = question.hasSignature ?? false;
    final navigatesToQPMDPage = questionParts.length == 1 && !hasSignature;

    int measurementCount;
    if (navigatesToQPMDPage) {
      // For QPMDPage navigation, calculate visible measurements considering conditional logic
      measurementCount = _calculateInitialVisibleMeasurementsForQPMD(question);
    } else {
      // For other navigation types, use all measurements
      measurementCount = question.measurements!.length;
    }

    final totalExpectedAnswers = measurementCount * questionPartCount;

    int answeredCount = 0;

    if (widget.taskId != null &&
        widget.form.formId != null &&
        question.questionId != null) {
      try {
        final realm = RealmDatabase.instance.realm;

        // Find the task with the matching taskId
        final taskModel = realm.query<TaskDetailModel>(
            'taskId == \$0', [widget.taskId!.toInt()]).firstOrNull;

        if (taskModel != null) {
          // Find the form with the matching formId
          final formModel = taskModel.forms
              .where((form) => form.formId == widget.form.formId!.toInt())
              .firstOrNull;

          if (formModel != null) {
            // Get saved answers for this question
            final savedAnswers = formModel.questionAnswers
                .where((answer) =>
                    answer.questionId == question.questionId!.toInt())
                .toList();

            if (questionPartCount > 1) {
              // For questions with multiple parts, count unique measurement-questionpart combinations
              final answeredCombinations = <String>{};
              for (final answer in savedAnswers) {
                if (answer.measurementId != null &&
                    answer.questionpartId != null) {
                  final combinationKey =
                      '${answer.measurementId}-${answer.questionpartId}';
                  answeredCombinations.add(combinationKey);
                }
              }
              answeredCount = answeredCombinations.length;
            } else {
              // For questions with single part, count unique measurements
              final answeredMeasurementIds = <int>{};
              for (final answer in savedAnswers) {
                if (answer.measurementId != null) {
                  answeredMeasurementIds.add(answer.measurementId!);
                }
              }
              answeredCount = answeredMeasurementIds.length;
            }
          }
        }
      } catch (e) {
        // Error calculating progress - use default values
      }
    }

    // Calculate progress percentage
    progress =
        totalExpectedAnswers > 0 ? answeredCount / totalExpectedAnswers : 0.0;
    progressText = '$answeredCount of $totalExpectedAnswers';

    return {'progress': progress, 'progressText': progressText};
  }

  /// Calculate the initial number of visible measurements for QPMDPage
  /// This simulates the conditional logic that QPMDPage uses to determine initial widget visibility
  int _calculateInitialVisibleMeasurementsForQPMD(entities.Question question) {
    if (question.measurements == null || question.measurements!.isEmpty) {
      return 0;
    }

    final measurements = question.measurements!;
    final widgetVisibility = <num, bool>{};

    // Step 1: Initialize visibility based on conditional logic (similar to QPMDPage._getInitialVisibility)
    for (final measurement in measurements) {
      if (measurement.measurementId == null) continue;

      widgetVisibility[measurement.measurementId!] =
          _getInitialVisibilityForMeasurement(
              measurement, measurements, question.isMll);
    }

    // Step 2: Process any pre-selected values from saved data (similar to QPMDPage._processInitialConditionalLogic)
    if (widget.taskId != null &&
        widget.form.formId != null &&
        question.questionId != null) {
      try {
        final realm = RealmDatabase.instance.realm;
        final taskModel = realm.query<TaskDetailModel>(
            'taskId == \$0', [widget.taskId!.toInt()]).firstOrNull;

        if (taskModel != null) {
          final formModel = taskModel.forms
              .where((form) => form.formId == widget.form.formId!.toInt())
              .firstOrNull;

          if (formModel != null) {
            final savedAnswers = formModel.questionAnswers
                .where((answer) =>
                    answer.questionId == question.questionId!.toInt())
                .toList();

            // Process conditional logic for measurements with saved values
            for (final measurement in measurements) {
              if (measurement.measurementId == null) continue;

              if (_canMeasurementTriggerConditionalLogic(measurement)) {
                // Find saved value for this measurement
                final savedAnswer = savedAnswers
                    .where((answer) =>
                        answer.measurementId ==
                        measurement.measurementId!.toInt())
                    .firstOrNull;

                if (savedAnswer != null) {
                  final savedValue = _extractSavedValue(
                      savedAnswer, measurement.measurementTypeId);
                  if (savedValue != null &&
                      !_isValueEmptyForCalculation(
                          savedValue, measurement.measurementTypeId)) {
                    // Apply conditional logic based on saved value
                    _applyConditionalLogicForCalculation(measurement,
                        savedValue, widgetVisibility, question.isMll);
                  }
                }
              }
            }
          }
        }
      } catch (e) {
        // Error loading saved data - use default visibility
      }
    }

    // Step 3: Count visible measurements (excluding PhotoUploadWidget components)
    int visibleCount = 0;
    for (final measurement in measurements) {
      if (measurement.measurementId == null) continue;

      final isVisible = widgetVisibility[measurement.measurementId!] ?? true;
      if (isVisible) {
        visibleCount++;
      }
    }

    return visibleCount;
  }

  /// Determine initial visibility for a measurement widget (similar to QPMDPage._getInitialVisibility)
  /// Widgets that are targets of conditional actions should be hidden by default
  bool _getInitialVisibilityForMeasurement(entities.Measurement measurement,
      List<entities.Measurement> measurements, bool? isMll) {
    if (measurement.measurementId == null) return true;

    // Check if this measurement is a target of any conditional action
    for (final otherMeasurement in measurements) {
      if (otherMeasurement.measurementId == measurement.measurementId) continue;

      // Check measurement_conditions array
      if (otherMeasurement.measurementConditions != null) {
        for (final condition in otherMeasurement.measurementConditions!) {
          if (condition.actionMeasurementId == measurement.measurementId) {
            // This measurement is a target of conditional logic
            // Hide it by default if the action is 'appear'
            if (condition.action?.toLowerCase() == 'appear') {
              return false;
            }
          }
        }
      }

      // Check measurement_conditions_multiple array
      if (otherMeasurement.measurementConditionsMultiple != null) {
        for (final condition
            in otherMeasurement.measurementConditionsMultiple!) {
          if (condition.actionMeasurementId == measurement.measurementId) {
            // This measurement is a target of conditional logic
            // Hide it by default if the action is 'appear'
            if (condition.action?.toLowerCase() == 'appear') {
              return false;
            }
          }
        }
      }
    }

    // Default to visible if not a target of conditional logic
    return true;
  }

  /// Check if a measurement can trigger conditional logic (similar to QPMDPage._canTriggerConditionalLogic)
  bool _canMeasurementTriggerConditionalLogic(
      entities.Measurement measurement) {
    // Check if this measurement has any conditional logic defined
    final hasConditions =
        (measurement.measurementConditions?.isNotEmpty ?? false) ||
            (measurement.measurementConditionsMultiple?.isNotEmpty ?? false);

    // Only certain widget types can trigger conditional logic
    final canTrigger = [3, 4, 5, 6, 8].contains(measurement.measurementTypeId);

    return hasConditions && canTrigger;
  }

  /// Extract saved value from QuestionAnswerModel based on measurement type
  dynamic _extractSavedValue(
      QuestionAnswerModel answer, num? measurementTypeId) {
    switch (measurementTypeId) {
      case 1: // Text field
      case 2: // Text field
      case 7: // Counter
      case 9: // Date picker
        return answer.measurementTextResult;
      case 3: // Checkbox
        return answer.measurementOptionId?.toString();
      case 4: // Dropdown
      case 5: // Radio
      case 8: // Radio
        return answer.measurementOptionId;
      case 6: // Multi-select
        return answer.measurementOptionIds;
      default:
        return null;
    }
  }

  /// Check if a value is empty for calculation purposes
  bool _isValueEmptyForCalculation(dynamic value, num? measurementTypeId) {
    if (value == null) return true;

    switch (measurementTypeId) {
      case 1: // Text field
      case 2: // Text field
      case 7: // Counter
      case 9: // Date picker
        return value.toString().trim().isEmpty;
      case 3: // Checkbox
      case 4: // Dropdown
      case 5: // Radio
      case 8: // Radio
        return value == null;
      case 6: // Multi-select
        if (value is List) {
          return value.isEmpty;
        }
        return value.toString().trim().isEmpty;
      default:
        return true;
    }
  }

  /// Apply conditional logic for calculation purposes (similar to QPMDPage._processConditionalLogic)
  void _applyConditionalLogicForCalculation(entities.Measurement measurement,
      dynamic selectedValue, Map<num, bool> widgetVisibility, bool? isMll) {
    if (isMll == null || selectedValue == null) return;

    // Get the selected option ID based on widget type
    final selectedOptionId =
        _getSelectedOptionIdForCalculation(measurement, selectedValue);
    if (selectedOptionId == null) return;

    // Check conditions based on is_mll property
    if (isMll == false) {
      // Use measurement_conditions array
      _processConditionsForCalculation(
        measurement.measurementConditions,
        measurement.measurementId,
        selectedOptionId,
        widgetVisibility,
      );
    } else {
      // Use measurement_conditions_multiple array
      _processConditionsForCalculation(
        measurement.measurementConditionsMultiple,
        measurement.measurementId,
        selectedOptionId,
        widgetVisibility,
      );
    }
  }

  /// Get selected option ID for conditional logic calculation
  num? _getSelectedOptionIdForCalculation(
      entities.Measurement measurement, dynamic selectedValue) {
    switch (measurement.measurementTypeId) {
      case 3: // Checkbox
        // For checkboxes, convert boolean to option ID (1 for true, 2 for false)
        if (selectedValue is String) {
          final optionId = int.tryParse(selectedValue);
          return optionId;
        }
        return null;
      case 4: // Dropdown
      case 5: // Radio
      case 8: // Radio
        if (selectedValue is num) {
          return selectedValue;
        }
        return null;
      case 6: // Multi-select
        // For multi-select, we need to check each selected option
        // This is more complex and would require checking all conditions
        // For now, return null to skip multi-select conditional logic in calculation
        return null;
      default:
        return null;
    }
  }

  /// Process conditions for calculation purposes
  void _processConditionsForCalculation(
      List<dynamic>? conditions,
      num? measurementId,
      num selectedOptionId,
      Map<num, bool> widgetVisibility) {
    if (conditions == null || measurementId == null) return;

    for (final condition in conditions) {
      if (condition.measurementId == measurementId &&
          condition.measurementOptionId == selectedOptionId &&
          condition.actionMeasurementId != null) {
        final action = condition.action?.toLowerCase();
        if (action == 'appear') {
          widgetVisibility[condition.actionMeasurementId!] = true;
        } else if (action == 'disappear') {
          widgetVisibility[condition.actionMeasurementId!] = false;
        }
      }
    }
  }

  /// Calculate progress for multi-questions with dynamic form count display logic
  Map<String, dynamic> _calculateMultiQuestionProgress(
      entities.Question question) {
    double progress = 0.0;
    String progressText = '0 of ?';

    if (widget.taskId == null ||
        widget.form.formId == null ||
        question.questionId == null) {
      return {'progress': progress, 'progressText': progressText};
    }

    try {
      final realm = RealmDatabase.instance.realm;

      // Find the task with the matching taskId
      final taskModel = realm.query<TaskDetailModel>(
          'taskId == \$0', [widget.taskId!.toInt()]).firstOrNull;

      if (taskModel == null) {
        return {'progress': progress, 'progressText': progressText};
      }

      // Find the form with the matching formId
      final formModel = taskModel.forms
          .where((form) => form.formId == widget.form.formId!.toInt())
          .firstOrNull;

      if (formModel == null) {
        return {'progress': progress, 'progressText': progressText};
      }

      // Check if this is a restricted multi-question
      final isRestrictedMultiQuestion = question.multiMeasurementId != null &&
          question.multiMeasurementId != 0;

      if (isRestrictedMultiQuestion) {
        // For restricted multi-questions: Use savedValueCount from existing database queries
        final savedValueCount =
            _getSavedValueCountForRestrictedMultiQuestion(question, formModel);

        // Get all answers for this question
        final savedAnswers = formModel.questionAnswers
            .where(
                (answer) => answer.questionId == question.questionId!.toInt())
            .toList();

        // Count unique questionpartMultiId entries that have been selected (added parts)
        final selectedParts = <String>{};
        for (final answer in savedAnswers) {
          if (answer.questionPartMultiId != null &&
              answer.questionPartMultiId!.isNotEmpty) {
            selectedParts.add(answer.questionPartMultiId!);
          }
        }

        // Count unique questionpartMultiId entries that have actual measurement data (completed parts)
        final completedParts = <String>{};
        for (final answer in savedAnswers) {
          if (answer.questionPartMultiId != null &&
              answer.questionPartMultiId!.isNotEmpty &&
              _hasActualMeasurementData(answer)) {
            completedParts.add(answer.questionPartMultiId!);
          }
        }

        final totalCount = savedValueCount > 0
            ? savedValueCount
            : 1; // Show at least 1 if no saved count
        final currentProgress = completedParts.length;

        progress = totalCount > 0 ? currentProgress / totalCount : 0.0;
        progressText = '$currentProgress of $totalCount';

        debugPrint(
            'RestrictedMultiQuestion ${question.questionId}: savedValueCount=$savedValueCount, selectedParts=${selectedParts.length}, completedParts=$currentProgress, progressText=$progressText');
      } else {
        // For regular multi-questions: Count dynamically added measurement parts
        final savedAnswers = formModel.questionAnswers
            .where(
                (answer) => answer.questionId == question.questionId!.toInt())
            .toList();

        // Count questionpartMultiId items with 'a-b' format (dynamically added parts)
        final dynamicParts = <String>{};
        for (final answer in savedAnswers) {
          if (answer.questionPartMultiId != null &&
              answer.questionPartMultiId!.contains('-')) {
            dynamicParts.add(answer.questionPartMultiId!);
          }
        }

        // Count questionpartMultiId items with 'a-b' format that have actual measurement data (completed parts)
        final completedDynamicParts = <String>{};
        for (final answer in savedAnswers) {
          if (answer.questionPartMultiId != null &&
              answer.questionPartMultiId!.contains('-') &&
              _hasActualMeasurementData(answer)) {
            completedDynamicParts.add(answer.questionPartMultiId!);
          }
        }

        final addedPartsCount = dynamicParts.length;
        final completedPartsCount = completedDynamicParts.length;

        if (addedPartsCount > 0) {
          // When parts have been added: Show "X of Y" where X is completed and Y is total added
          progress =
              addedPartsCount > 0 ? completedPartsCount / addedPartsCount : 0.0;
          progressText = '$completedPartsCount of $addedPartsCount';
        } else {
          // When no parts are added: Show "0 of ?" to indicate unknown total
          progress = 0.0;
          progressText = '0 of ?';
        }

        debugPrint(
            'RegularMultiQuestion ${question.questionId}: addedPartsCount=$addedPartsCount, completedPartsCount=$completedPartsCount, progressText=$progressText');
      }
    } catch (e) {
      debugPrint('Error calculating multi-question progress: $e');
      // Return default values on error
    }

    return {'progress': progress, 'progressText': progressText};
  }

  /// Check if a QuestionAnswer has actual measurement data (not just selection data)
  /// Selection entries from FQPDPage have questionpartId and questionPartMultiId but no measurement data
  /// Measurement entries from QPMDPage have questionpartId, questionPartMultiId AND measurement data
  bool _hasActualMeasurementData(QuestionAnswerModel answer) {
    // Check if the answer has actual measurement data
    // An answer is considered to have measurement data if it has:
    // 1. A measurementId (indicates it's tied to a specific measurement)
    // 2. Some form of measurement result (text, option, or option IDs)

    if (answer.measurementId == null) {
      return false; // No measurement ID means it's just a selection entry
    }

    // Check if it has any measurement result data
    final hasTextResult = answer.measurementTextResult != null &&
        answer.measurementTextResult!.isNotEmpty;
    final hasOptionId = answer.measurementOptionId != null;
    final hasOptionIds = answer.measurementOptionIds != null &&
        answer.measurementOptionIds!.isNotEmpty;

    return hasTextResult || hasOptionId || hasOptionIds;
  }

  /// Get saved value count for restricted multi-question from database
  /// This looks at counter values from OTHER questions (not the restrictedMultiQuestion itself)
  int _getSavedValueCountForRestrictedMultiQuestion(
      entities.Question question, FormModel formModel) {
    try {
      int maxCount = 0;

      // Get all question answers for this form
      final allQuestionAnswers = formModel.questionAnswers.toList();

      // Group by questionId to find counter values from other questions
      final questionGroups = <int, List<QuestionAnswerModel>>{};
      for (final answer in allQuestionAnswers) {
        if (answer.questionId != null) {
          questionGroups.putIfAbsent(answer.questionId!, () => []).add(answer);
        }
      }

      // Check each question group (excluding the restrictedMultiQuestion itself)
      for (final entry in questionGroups.entries) {
        final questionId = entry.key;
        final answers = entry.value;

        // Skip the restrictedMultiQuestion itself
        if (questionId == question.questionId!.toInt()) {
          continue;
        }

        // Look for counter values in this question's answers
        for (final answer in answers) {
          if (answer.measurementTextResult != null) {
            final counterValue = int.tryParse(answer.measurementTextResult!);
            if (counterValue != null && counterValue > maxCount) {
              maxCount = counterValue;
            }
          }
        }
      }

      return maxCount;
    } catch (e) {
      debugPrint(
          'Error getting saved value count for restrictedMultiQuestion: $e');
      return 0;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.lightGrey2,
      appBar: CustomAppBar(
        title: widget.form.formName ?? 'Questions',
        actions: [
          IconButton(
            icon: const Icon(
              Icons.save_rounded,
              color: AppColors.primaryBlue,
            ),
            onPressed: _handleSave,
          ),
        ],
      ),
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Gap(8),
            visibleQuestions.isEmpty
                ? const EmptyQuestionsState()
                : ListView.separated(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    padding: const EdgeInsets.symmetric(
                        horizontal: 16, vertical: 8.0),
                    itemCount: visibleQuestions.length,
                    itemBuilder: (context, index) {
                      final question = visibleQuestions[index];

                      if (question.isComment == true &&
                          question.questionId != null) {
                        final questionId = question.questionId!;
                        // Ensure controller and selected value exists, though initState should handle this
                        if (!_commentControllers.containsKey(questionId)) {
                          _commentControllers[questionId] =
                              TextEditingController();
                        }
                        if (!_selectedCommentTypes.containsKey(questionId)) {
                          _selectedCommentTypes[questionId] = null;
                        }

                        return CommentCard(
                          question: question,
                          selectedCommentType:
                              _selectedCommentTypes[questionId],
                          commentController: _commentControllers[questionId]!,
                          onCommentTypeChanged: (String? newValue) {
                            setState(() {
                              _selectedCommentTypes[questionId] = newValue;
                              // Clear validation error when value changes
                              _commentValidationErrors[questionId] = null;
                            });
                          },
                          errorText: _commentValidationErrors[questionId],
                        );
                      } else {
                        // Calculate progress based on saved QuestionAnswer data
                        final progressData =
                            _calculateQuestionProgress(question);
                        final progress = progressData['progress'] as double;
                        final progressText =
                            progressData['progressText'] as String;

                        final isMandatory = _isQuestionMandatory(question);
                        final cameraInfo = _getCameraIconInfo(question);
                        final hasPhoto = _hasPhotoUrl(question);

                        return QuestionCard(
                          question: question,
                          progress: progress,
                          progressText: progressText,
                          isMandatory: isMandatory,
                          showCameraIcon: cameraInfo['show'] as bool,
                          isCameraMandatory: cameraInfo['isMandatory'] as bool,
                          hasPhotoUrl: hasPhoto,
                          taskId: widget.taskId,
                          formId: widget.form.formId,
                          onQuestionTap: () => _handleQuestionTap(question),
                          onPhotoSectionTap: () =>
                              _handleAddPhotosTap(question),
                          photoRefreshCounter: _photoRefreshCounter,
                        );
                      }
                    },
                    separatorBuilder: (BuildContext context, int index) {
                      return const Gap(8);
                    },
                  ),
            const Gap(8),
          ],
        ),
      ),
    );
  }
}
